<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Organizer - Organize PDF Pages Online</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Local CSS -->
    <link rel="stylesheet" href="style.css">

</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <i class="fas fa-file-pdf"></i>
                <h1>PDF Organizer</h1>
            </div>
           
        </div>
    </header>

    <main>
        <section id="hero" class="hero-section">
            <div class="container">
                <!-- ...existing hero-content and upload area... -->
                <div class="hero-content">
                    <h2>Organize PDF Pages</h2>
                    <p>Sort, add and delete PDF pages. Drag and drop the page thumbnails and sort them in our PDF organizer.</p>
                    <div class="upload-area" id="uploadArea">
                        <div class="upload-content">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <h3>Select PDF file</h3>
                            <p>or drop PDF here</p>
                            <input type="file" id="fileInput" accept=".pdf" multiple>
                            <button class="btn-primary" onclick="document.getElementById('fileInput').click()">
                                Choose PDF files
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="editor" class="editor-section" style="display: none;">
            <div class="container">
                <div class="editor-header">
                    <h3>Organize your PDF</h3>
                    <div class="editor-controls">
                        <button class="btn-secondary" id="addPageBtn">
                            <i class="fas fa-plus"></i> Add Blank Page
                        </button>
                        <button class="btn-secondary" id="deleteSelectedBtn">
                            <i class="fas fa-trash"></i> Delete Selected
                        </button>
                        <button class="btn-primary" id="downloadBtn">
                            <i class="fas fa-download"></i> Download PDF
                        </button>
                    </div>
                </div>
                
                <div class="pages-container" id="pagesContainer">
                    <!-- PDF pages will be dynamically added here -->
                </div>
                
                <div class="editor-footer">
                    <button class="btn-secondary" id="resetBtn">
                        <i class="fas fa-undo"></i> Reset
                    </button>
                    <button class="btn-primary" id="processBtn">
                        <i class="fas fa-cog"></i> Process PDF
                    </button>
                </div>
            </div>
        </section>


    </main>

    <div id="loadingModal" class="modal">
        <div class="modal-content">
            <div class="loading-spinner"></div>
            <p>Processing your PDF...</p>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf-lib/1.17.1/pdf-lib.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>

    <!-- Local JS -->
    <script src="script.js"></script>

</body>
</html>
