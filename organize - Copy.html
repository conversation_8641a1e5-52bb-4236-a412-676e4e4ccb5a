<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Organize PDF Files</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #fff;
            color: #333;
        }

        /* Home Page Styles */
        .home-page {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            background-color: #fff;
        }

        .main-title {
            font-size: 3rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 1rem;
        }

        .main-description {
            font-size: 1.2rem;
            color: #6c757d;
            margin-bottom: 3rem;
        }

        .file-upload-btn {
            width: 320px;
            height: 90px;
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 1.2rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }

        .file-upload-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
        }

        #fileInput {
            display: none;
        }

        /* Organizer Page Styles */
        .organizer-page {
            display: none;
            min-height: 100vh;
            background-color: #fff;
            padding: 20px 0;
        }

        .organizer-container {
            width: 800px;
            height: 400px;
            margin: 0 auto;
            background: #fff;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .organizer-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
        }

        .organizer-header h3 {
            font-size: 1.5rem;
            color: #2c3e50;
            margin: 0;
        }

        .files-grid {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
            gap: 15px;
            align-content: start;
        }

        .file-card {
            width: 180px;
            height: 265px;
            background: #fff;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            cursor: move;
            transition: all 0.3s ease;
            position: relative;
            display: flex;
            flex-direction: column;
        }

        .file-card:hover {
            border-color: #e74c3c;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .file-card.selected {
            border-color: #e74c3c;
            background: #fff5f5;
        }

        .file-preview {
            width: 100%;
            height: 180px;
            background: #f8f9fa;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            overflow: hidden;
        }

        .file-preview canvas {
            max-width: 100%;
            max-height: 100%;
            border-radius: 5px;
        }

        .file-preview .pdf-icon {
            font-size: 3rem;
            color: #e74c3c;
        }

        .file-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .file-name {
            font-size: 0.9rem;
            font-weight: 600;
            color: #2c3e50;
            text-align: center;
            margin-bottom: 10px;
            word-break: break-word;
        }

        .file-actions {
            display: flex;
            justify-content: space-between;
            gap: 5px;
        }

        .action-btn {
            flex: 1;
            padding: 6px;
            border: none;
            border-radius: 5px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .delete-btn {
            background: #e74c3c;
            color: white;
        }

        .delete-btn:hover {
            background: #c0392b;
        }

        .select-btn {
            background: #3498db;
            color: white;
        }

        .select-btn:hover {
            background: #2980b9;
        }

        .start-over-btn {
            background: #6c757d;
            color: white;
        }

        .start-over-btn:hover {
            background: #5a6268;
        }

        .organizer-footer {
            background: #f8f9fa;
            padding: 20px;
            border-top: 1px solid #e9ecef;
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .footer-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #e74c3c;
            color: white;
        }

        .btn-primary:hover {
            background: #c0392b;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-1px);
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #218838;
            transform: translateY(-1px);
        }

        /* Drag and Drop Styles */
        .file-card.dragging {
            opacity: 0.5;
            transform: rotate(5deg);
        }

        .files-grid.drag-over {
            background: rgba(231, 76, 60, 0.05);
        }

        /* Loading Modal */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 2000;
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background: white;
            padding: 3rem;
            border-radius: 15px;
            text-align: center;
            max-width: 400px;
            width: 90%;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid #e74c3c;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .main-title {
                font-size: 2rem;
            }

            .file-upload-btn {
                width: 280px;
                height: 80px;
                font-size: 1.1rem;
            }

            .organizer-container {
                width: 95%;
                height: 500px;
            }

            .files-grid {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            }

            .file-card {
                width: 150px;
                height: 220px;
            }

            .file-preview {
                height: 140px;
            }
        }
    </style>
</head>
<body>
    <!-- Home Page -->
    <div id="homePage" class="home-page">
        <h1 class="main-title">Organize PDF Files</h1>
        <p class="main-description">You can organize PDF files easily</p>
        <button class="file-upload-btn" onclick="document.getElementById('fileInput').click()">
            Choose PDF Files
        </button>
        <input type="file" id="fileInput" accept=".pdf" multiple>
    </div>

    <!-- Organizer Page -->
    <div id="organizerPage" class="organizer-page">
        <div class="organizer-container">
            <div class="organizer-header">
                <h3 id="headerTitle">Organize your PDF files</h3>
            </div>

            <div class="files-grid" id="filesGrid">
                <!-- File cards will be dynamically added here -->
            </div>

            <div class="organizer-footer">
                <button class="footer-btn btn-secondary" id="sortBtn">
                    <i class="fas fa-sort"></i> Sort Pages
                </button>
                <button class="footer-btn btn-secondary" id="mergeBtn">
                    <i class="fas fa-compress-arrows-alt"></i> Merge Files
                </button>
                <button class="footer-btn btn-success" id="downloadBtn">
                    <i class="fas fa-download"></i> Download PDF
                </button>
                <button class="footer-btn btn-secondary" id="resetBtn">
                    <i class="fas fa-undo"></i> Start Over
                </button>
            </div>
        </div>
    </div>

    <!-- Loading Modal -->
    <div id="loadingModal" class="modal">
        <div class="modal-content">
            <div class="loading-spinner"></div>
            <p>Processing your PDF...</p>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf-lib/1.17.1/pdf-lib.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="script.js"></script>
</body>
</html>
