       /* styles.css content starts here */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        header {
            background: #fff;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }

        header .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 20px;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .logo i {
            font-size: 2rem;
            color: #e74c3c;
        }

        .logo h1 {
            font-size: 1.5rem;
            color: #2c3e50;
        }

        nav ul {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        nav a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            transition: color 0.3s;
        }

        nav a:hover {
            color: #e74c3c;
        }

        /* Main Content */
        main {
            margin-top: 80px;
        }

        /* Hero Section */
        .hero-section {
            padding: 4rem 0;
            background: linear-gradient(135deg, #878fb4 0%, #764ba2 100%);
            color: white;
            text-align: center;
        }

        .hero-content h2 {
            font-size: 3rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }

        .hero-content p {
            font-size: 1.2rem;
            margin-bottom: 3rem;
            opacity: 0.9;
        }

        /* Upload Area */
        .upload-area {
            background: rgba(255,255,255,0.1);
            border: 3px dashed rgba(255,255,255,0.3);
            border-radius: 15px;
            padding: 3rem;
            margin: 2rem auto;
            max-width: 600px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            background: rgba(255,255,255,0.15);
            border-color: rgba(255,255,255,0.5);
        }

        .upload-area.dragover {
            background: rgba(255,255,255,0.2);
            border-color: #fff;
            transform: scale(1.02);
        }

        .upload-content i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.8;
        }

        .upload-content h3 {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
        }

        .upload-content p {
            margin-bottom: 2rem;
            opacity: 0.8;
        }

        #fileInput {
            display: none;
        }

        /* Buttons */
        .btn-primary, .btn-secondary {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #e74c3c;
            color: white;
        }

        .btn-primary:hover {
            background: #c0392b;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }

        /* Editor Section */
        .editor-section {
            padding: 3rem 0;
            background: #fff;
        }

        .editor-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .editor-header h3 {
            font-size: 2rem;
            color: #2c3e50;
        }

        .editor-controls {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        /* Pages Container */
        .pages-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            margin: 2rem 0;
            min-height: 300px;
            padding: 20px;
            border: 2px dashed #ddd;
            border-radius: 10px;
            background: #f8f9fa;
        }

        .page-item {
            background: white;
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            cursor: move;
            transition: all 0.3s ease;
            position: relative;
            user-select: none;
        }

        .page-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .page-item.selected {
            border: 3px solid #e74c3c;
            transform: scale(1.05);
        }

        .page-item.dragging {
            opacity: 0.5;
            transform: rotate(5deg);
        }

        .page-preview {
            width: 100%;
            height: 200px;
            background: #f0f0f0;
            border-radius: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            overflow: hidden;
        }

        .page-preview canvas {
            max-width: 100%;
            max-height: 100%;
            border-radius: 5px;
        }

        .page-preview .blank-page {
            font-size: 3rem;
            color: #ccc;
        }

        .page-info {
            text-align: center;
        }

        .page-number {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .page-actions {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 10px;
        }

        .page-actions button {
            padding: 5px 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.3s ease;
        }

        .delete-btn {
            background: #e74c3c;
            color: white;
        }

        .delete-btn:hover {
            background: #c0392b;
        }

        .select-btn {
            background: #3498db;
            color: white;
        }

        .select-btn:hover {
            background: #2980b9;
        }

        /* Editor Footer */
        .editor-footer {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-top: 2rem;
        }

        /* Modal */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            z-index: 2000;
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background: white;
            padding: 3rem;
            border-radius: 15px;
            text-align: center;
            max-width: 400px;
            width: 90%;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid #e74c3c;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero-content h2 {
                font-size: 2rem;
            }
            
            .hero-content p {
                font-size: 1rem;
            }
            
            .upload-area {
                padding: 2rem 1rem;
            }
            
            .editor-header {
                flex-direction: column;
                align-items: stretch;
            }
            
            .editor-controls {
                justify-content: center;
            }
            
            .pages-container {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
                gap: 15px;
            }
            
            .page-preview {
                height: 150px;
            }
            
            nav ul {
                gap: 1rem;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 0 15px;
            }
            
            .hero-content h2 {
                font-size: 1.5rem;
            }
            
            .pages-container {
                grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            }
            
            .page-preview {
                height: 120px;
            }
            
            .editor-controls {
                flex-direction: column;
            }
            
            .btn-primary, .btn-secondary {
                width: 100%;
                justify-content: center;
            }
        }

        /* Drag and Drop Styles */
        .page-item.drag-over {
            border: 3px dashed #e74c3c;
            background: rgba(231, 76, 60, 0.1);
        }

        .pages-container.drag-over {
            border-color: #e74c3c;
            background: rgba(231, 76, 60, 0.05);
        }

        /* Animation for new pages */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .page-item.new-page {
            animation: fadeInUp 0.5s ease;
        }
        /* styles.css content ends here */