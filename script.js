    // script.js content starts here
        class PDFOrganizer {
            constructor() {
                this.pdfDoc = null;
                this.pages = [];
                this.selectedPages = new Set();
                this.draggedElement = null;
                this.init();
            }

            init() {
                this.setupEventListeners();
                this.setupDragAndDrop();
                
                // Set PDF.js worker
                if (typeof pdfjsLib !== 'undefined') {
                    pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
                }
            }

            setupEventListeners() {
                const fileInput = document.getElementById('fileInput');
                const uploadArea = document.getElementById('uploadArea');
                const addPageBtn = document.getElementById('addPageBtn');
                const deleteSelectedBtn = document.getElementById('deleteSelectedBtn');
                const downloadBtn = document.getElementById('downloadBtn');
                const resetBtn = document.getElementById('resetBtn');
                const processBtn = document.getElementById('processBtn');

                // File input change
                fileInput.addEventListener('change', (e) => this.handleFileSelect(e));

                // Upload area drag and drop
                uploadArea.addEventListener('dragover', (e) => this.handleDragOver(e));
                uploadArea.addEventListener('drop', (e) => this.handleFileDrop(e));
                uploadArea.addEventListener('dragenter', (e) => this.handleDragEnter(e));
                uploadArea.addEventListener('dragleave', (e) => this.handleDragLeave(e));

                // Button clicks
                addPageBtn.addEventListener('click', () => this.addBlankPage());
                deleteSelectedBtn.addEventListener('click', () => this.deleteSelectedPages());
                downloadBtn.addEventListener('click', () => this.downloadPDF());
                resetBtn.addEventListener('click', () => this.reset());
                processBtn.addEventListener('click', () => this.processPDF());
            }

            setupDragAndDrop() {
                const pagesContainer = document.getElementById('pagesContainer');
                
                pagesContainer.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    pagesContainer.classList.add('drag-over');
                });
                
                pagesContainer.addEventListener('dragleave', (e) => {
                    if (!pagesContainer.contains(e.relatedTarget)) {
                        pagesContainer.classList.remove('drag-over');
                    }
                });
                
                pagesContainer.addEventListener('drop', (e) => {
                    e.preventDefault();
                    pagesContainer.classList.remove('drag-over');
                    this.handlePageDrop(e);
                });
            }

            handleFileSelect(event) {
                const files = event.target.files;
                if (files.length > 0) {
                    this.loadPDF(files[0]);
                }
            }

            handleDragOver(event) {
                event.preventDefault();
            }

            handleDragEnter(event) {
                event.preventDefault();
                document.getElementById('uploadArea').classList.add('dragover');
            }

            handleDragLeave(event) {
                if (!event.currentTarget.contains(event.relatedTarget)) {
                    document.getElementById('uploadArea').classList.remove('dragover');
                }
            }

            handleFileDrop(event) {
                event.preventDefault();
                document.getElementById('uploadArea').classList.remove('dragover');
                
                const files = event.dataTransfer.files;
                if (files.length > 0 && files[0].type === 'application/pdf') {
                    this.loadPDF(files[0]);
                } else {
                    this.showAlert('Please drop a valid PDF file.');
                }
            }

            async loadPDF(file) {
                try {
                    this.showLoading(true);
                    
                    const arrayBuffer = await file.arrayBuffer();
                    this.pdfDoc = await PDFLib.PDFDocument.load(arrayBuffer);
                    
                    await this.renderPages();
                    this.showEditor();
                    
                } catch (error) {
                    console.error('Error loading PDF:', error);
                    this.showAlert('Error loading PDF file. Please try again.');
                } finally {
                    this.showLoading(false);
                }
            }

            async renderPages() {
                const pagesContainer = document.getElementById('pagesContainer');
                pagesContainer.innerHTML = '';
                this.pages = [];
                this.selectedPages.clear();

                const pageCount = this.pdfDoc.getPageCount();
                
                for (let i = 0; i < pageCount; i++) {
                    const pageData = {
                        index: i,
                        type: 'pdf',
                        originalIndex: i
                    };
                    this.pages.push(pageData);
                    
                    const pageElement = await this.createPageElement(pageData, i);
                    pagesContainer.appendChild(pageElement);
                }
            }

            async createPageElement(pageData, displayIndex) {
                const pageDiv = document.createElement('div');
                pageDiv.className = 'page-item';
                pageDiv.draggable = true;
                pageDiv.dataset.pageIndex = displayIndex;
                
                const preview = document.createElement('div');
                preview.className = 'page-preview';
                
                if (pageData.type === 'pdf') {
                    try {
                        // Use PDF.js to render page preview
                        const loadingTask = pdfjsLib.getDocument({data: await this.pdfDoc.save()});
                        const pdf = await loadingTask.promise;
                        const page = await pdf.getPage(pageData.index + 1);
                        
                        const scale = 1.5;
                        const viewport = page.getViewport({scale});
                        
                        const canvas = document.createElement('canvas');
                        const context = canvas.getContext('2d');
                        canvas.height = viewport.height;
                        canvas.width = viewport.width;
                        
                        await page.render({
                            canvasContext: context,
                            viewport: viewport
                        }).promise;
                        
                        preview.appendChild(canvas);
                    } catch (error) {
                        console.error('Error rendering page preview:', error);
                        preview.innerHTML = '<i class="fas fa-file-pdf" style="font-size: 3rem; color: #ccc;"></i>';
                    }
                } else {
                    // Blank page
                    preview.innerHTML = '<i class="fas fa-file blank-page"></i>';
                }
                
                const info = document.createElement('div');
                info.className = 'page-info';
                
                const pageNumber = document.createElement('div');
                pageNumber.className = 'page-number';
                pageNumber.textContent = `Page ${displayIndex + 1}`;
                
                const actions = document.createElement('div');
                actions.className = 'page-actions';
                
                const selectBtn = document.createElement('button');
                selectBtn.className = 'select-btn';
                selectBtn.innerHTML = '<i class="fas fa-check"></i>';
                selectBtn.onclick = (e) => {
                    e.stopPropagation();
                    this.togglePageSelection(displayIndex, pageDiv);
                };
                
                const deleteBtn = document.createElement('button');
                deleteBtn.className = 'delete-btn';
                deleteBtn.innerHTML = '<i class="fas fa-trash"></i>';
                deleteBtn.onclick = (e) => {
                    e.stopPropagation();
                    this.deletePage(displayIndex);
                };
                
                actions.appendChild(selectBtn);
                actions.appendChild(deleteBtn);
                
                info.appendChild(pageNumber);
                info.appendChild(actions);
                
                pageDiv.appendChild(preview);
                pageDiv.appendChild(info);
                
                // Add drag event listeners
                pageDiv.addEventListener('dragstart', (e) => this.handlePageDragStart(e, displayIndex));
                pageDiv.addEventListener('dragend', (e) => this.handlePageDragEnd(e));
                
                return pageDiv;
            }

            handlePageDragStart(event, pageIndex) {
                this.draggedElement = event.target;
                this.draggedPageIndex = pageIndex;
                event.target.classList.add('dragging');
                event.dataTransfer.effectAllowed = 'move';
                event.dataTransfer.setData('text/html', event.target.outerHTML);
            }

            handlePageDragEnd(event) {
                event.target.classList.remove('dragging');
                this.draggedElement = null;
                this.draggedPageIndex = null;
            }

            handlePageDrop(event) {
                if (this.draggedPageIndex === null) return;
                
                const dropTarget = event.target.closest('.page-item');
                if (!dropTarget) return;
                
                const dropIndex = parseInt(dropTarget.dataset.pageIndex);
                
                if (this.draggedPageIndex !== dropIndex) {
                    this.movePage(this.draggedPageIndex, dropIndex);
                }
            }

            movePage(fromIndex, toIndex) {
                const page = this.pages.splice(fromIndex, 1)[0];
                this.pages.splice(toIndex, 0, page);
                this.updatePageDisplay();
            }

            async updatePageDisplay() {
                const pagesContainer = document.getElementById('pagesContainer');
                pagesContainer.innerHTML = '';
                
                for (let i = 0; i < this.pages.length; i++) {
                    const pageElement = await this.createPageElement(this.pages[i], i);
                    pagesContainer.appendChild(pageElement);
                }
            }

            togglePageSelection(pageIndex, pageElement) {
                if (this.selectedPages.has(pageIndex)) {
                    this.selectedPages.delete(pageIndex);
                    pageElement.classList.remove('selected');
                } else {
                    this.selectedPages.add(pageIndex);
                    pageElement.classList.add('selected');
                }
            }

            addBlankPage() {
                const newPage = {
                    type: 'blank',
                    index: this.pages.length
                };
                this.pages.push(newPage);
                this.updatePageDisplay();
            }

            deletePage(pageIndex) {
                this.showConfirmation('Are you sure you want to delete this page?', () => {
                    this.pages.splice(pageIndex, 1);
                    this.selectedPages.delete(pageIndex);
                    this.updatePageDisplay();
                });
            }

            deleteSelectedPages() {
                if (this.selectedPages.size === 0) {
                    this.showAlert('Please select pages to delete.');
                    return;
                }
                
                this.showConfirmation(`Are you sure you want to delete ${this.selectedPages.size} selected page(s)?`, () => {
                    const sortedIndices = Array.from(this.selectedPages).sort((a, b) => b - a);
                    
                    sortedIndices.forEach(index => {
                        this.pages.splice(index, 1);
                    });
                    
                    this.selectedPages.clear();
                    this.updatePageDisplay();
                });
            }

            async processPDF() {
                if (!this.pdfDoc || this.pages.length === 0) {
                    this.showAlert('Please load a PDF file first.');
                    return;
                }
                
                try {
                    this.showLoading(true);
                    
                    const newPdf = await PDFLib.PDFDocument.create();
                    
                    for (const pageData of this.pages) {
                        if (pageData.type === 'pdf') {
                            const [copiedPage] = await newPdf.copyPages(this.pdfDoc, [pageData.index]);
                            newPdf.addPage(copiedPage);
                        } else if (pageData.type === 'blank') {
                            const blankPage = newPdf.addPage();
                            // Optionally add some content to blank page
                        }
                    }
                    
                    this.processedPdf = newPdf;
                    this.showAlert('PDF processed successfully! You can now download it.');
                    
                } catch (error) {
                    console.error('Error processing PDF:', error);
                    this.showAlert('Error processing PDF. Please try again.');
                } finally {
                    this.showLoading(false);
                }
            }

            async downloadPDF() {
                if (!this.processedPdf) {
                    await this.processPDF();
                }
                
                if (!this.processedPdf) {
                    return;
                }
                
                try {
                    this.showLoading(true);
                    
                    const pdfBytes = await this.processedPdf.save();
                    const blob = new Blob([pdfBytes], { type: 'application/pdf' });
                    const url = URL.createObjectURL(blob);
                    
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'organized-pdf.pdf';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                    
                } catch (error) {
                    console.error('Error downloading PDF:', error);
                    this.showAlert('Error downloading PDF. Please try again.');
                } finally {
                    this.showLoading(false);
                }
            }

            reset() {
                this.showConfirmation('Are you sure you want to reset? All changes will be lost.', () => {
                    this.pdfDoc = null;
                    this.processedPdf = null;
                    this.pages = [];
                    this.selectedPages.clear();
                    
                    document.getElementById('pagesContainer').innerHTML = '';
                    document.getElementById('fileInput').value = '';
                    this.hideEditor();
                });
            }

            showEditor() {
                document.getElementById('hero').style.display = 'none';
                document.getElementById('editor').style.display = 'block';
            }

            hideEditor() {
                document.getElementById('hero').style.display = 'block';
                document.getElementById('editor').style.display = 'none';
            }

            showLoading(show) {
                const modal = document.getElementById('loadingModal');
                modal.style.display = show ? 'flex' : 'none';
            }

            // Custom Alert/Confirmation Modals
            showAlert(message) {
                const alertModal = document.createElement('div');
                alertModal.className = 'modal';
                alertModal.style.display = 'flex';
                alertModal.innerHTML = `
                    <div class="modal-content">
                        <p>${message}</p>
                        <button class="btn-primary" style="margin-top: 20px;" onclick="document.body.removeChild(this.parentNode.parentNode)">OK</button>
                    </div>
                `;
                document.body.appendChild(alertModal);
            }

            showConfirmation(message, onConfirm) {
                const confirmModal = document.createElement('div');
                confirmModal.className = 'modal';
                confirmModal.style.display = 'flex';
                confirmModal.innerHTML = `
                    <div class="modal-content">
                        <p>${message}</p>
                        <div style="margin-top: 20px; display: flex; justify-content: center; gap: 10px;">
                            <button class="btn-primary" id="confirmYes">Yes</button>
                            <button class="btn-secondary" id="confirmNo">No</button>
                        </div>
                    </div>
                `;
                document.body.appendChild(confirmModal);

                document.getElementById('confirmYes').onclick = () => {
                    onConfirm();
                    document.body.removeChild(confirmModal);
                };
                document.getElementById('confirmNo').onclick = () => {
                    document.body.removeChild(confirmModal);
                };
            }
        }

        // Initialize the PDF Organizer when the page loads
        document.addEventListener('DOMContentLoaded', () => {
            new PDFOrganizer();
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
        // script.js content ends here