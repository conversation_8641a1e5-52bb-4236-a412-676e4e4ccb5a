    // script.js content starts here
        class PDFOrganizer {
            constructor() {
                this.files = [];
                this.selectedFiles = new Set();
                this.draggedElement = null;
                this.draggedIndex = null;
                this.init();
            }

            init() {
                this.setupEventListeners();
                this.setupDragAndDrop();

                // Set PDF.js worker
                if (typeof pdfjsLib !== 'undefined') {
                    pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
                }
            }

            setupEventListeners() {
                const fileInput = document.getElementById('fileInput');
                const downloadBtn = document.getElementById('downloadBtn');
                const resetBtn = document.getElementById('resetBtn');
                const sortBtn = document.getElementById('sortBtn');
                const mergeBtn = document.getElementById('mergeBtn');

                // File input change
                fileInput.addEventListener('change', (e) => this.handleFileSelect(e));

                // Button clicks
                downloadBtn.addEventListener('click', () => this.downloadPDF());
                resetBtn.addEventListener('click', () => this.reset());
                sortBtn.addEventListener('click', () => this.sortFiles());
                mergeBtn.addEventListener('click', () => this.mergeFiles());

                // Drag and drop on file input area
                const homePage = document.getElementById('homePage');
                homePage.addEventListener('dragover', (e) => this.handleDragOver(e));
                homePage.addEventListener('drop', (e) => this.handleFileDrop(e));
                homePage.addEventListener('dragenter', (e) => this.handleDragEnter(e));
                homePage.addEventListener('dragleave', (e) => this.handleDragLeave(e));
            }

            setupDragAndDrop() {
                const filesGrid = document.getElementById('filesGrid');

                filesGrid.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    filesGrid.classList.add('drag-over');
                });

                filesGrid.addEventListener('dragleave', (e) => {
                    if (!filesGrid.contains(e.relatedTarget)) {
                        filesGrid.classList.remove('drag-over');
                    }
                });

                filesGrid.addEventListener('drop', (e) => {
                    e.preventDefault();
                    filesGrid.classList.remove('drag-over');
                    this.handleFileDrop(e);
                });
            }

            handleFileSelect(event) {
                const files = Array.from(event.target.files);
                this.loadFiles(files);
            }

            handleDragOver(event) {
                event.preventDefault();
            }

            handleDragEnter(event) {
                event.preventDefault();
            }

            handleDragLeave(event) {
                event.preventDefault();
            }

            handleFileDrop(event) {
                event.preventDefault();

                const files = Array.from(event.dataTransfer.files);
                const pdfFiles = files.filter(file => file.type === 'application/pdf');

                if (pdfFiles.length > 0) {
                    this.loadFiles(pdfFiles);
                } else {
                    this.showAlert('Please drop valid PDF files.');
                }
            }

            async loadFiles(files) {
                try {
                    this.showLoading(true);

                    for (const file of files) {
                        const arrayBuffer = await file.arrayBuffer();
                        const pdfDoc = await PDFLib.PDFDocument.load(arrayBuffer);

                        const fileData = {
                            name: file.name,
                            file: file,
                            pdfDoc: pdfDoc,
                            pages: [],
                            selected: false
                        };

                        // Extract pages from PDF
                        const pageCount = pdfDoc.getPageCount();
                        for (let i = 0; i < pageCount; i++) {
                            fileData.pages.push({
                                index: i,
                                fileIndex: this.files.length
                            });
                        }

                        this.files.push(fileData);
                    }

                    await this.renderFiles();
                    this.showOrganizer();

                } catch (error) {
                    console.error('Error loading PDF files:', error);
                    this.showAlert('Error loading PDF files. Please try again.');
                } finally {
                    this.showLoading(false);
                }
            }

            async renderFiles() {
                const filesGrid = document.getElementById('filesGrid');
                const headerTitle = document.getElementById('headerTitle');

                filesGrid.innerHTML = '';
                this.selectedFiles.clear();

                if (this.files.length === 1) {
                    headerTitle.textContent = this.files[0].name;
                } else {
                    headerTitle.textContent = `${this.files.length} PDF files`;
                }

                for (let i = 0; i < this.files.length; i++) {
                    const fileElement = await this.createFileElement(this.files[i], i);
                    filesGrid.appendChild(fileElement);
                }
            }

            async createFileElement(fileData, fileIndex) {
                const fileDiv = document.createElement('div');
                fileDiv.className = 'file-card';
                fileDiv.draggable = true;
                fileDiv.dataset.fileIndex = fileIndex;

                const preview = document.createElement('div');
                preview.className = 'file-preview';

                try {
                    // Use PDF.js to render first page preview
                    const loadingTask = pdfjsLib.getDocument({data: await fileData.pdfDoc.save()});
                    const pdf = await loadingTask.promise;
                    const page = await pdf.getPage(1);

                    const scale = 0.8;
                    const viewport = page.getViewport({scale});

                    const canvas = document.createElement('canvas');
                    const context = canvas.getContext('2d');
                    canvas.height = viewport.height;
                    canvas.width = viewport.width;

                    await page.render({
                        canvasContext: context,
                        viewport: viewport
                    }).promise;

                    preview.appendChild(canvas);
                } catch (error) {
                    console.error('Error rendering file preview:', error);
                    preview.innerHTML = '<i class="fas fa-file-pdf pdf-icon"></i>';
                }

                const info = document.createElement('div');
                info.className = 'file-info';

                const fileName = document.createElement('div');
                fileName.className = 'file-name';
                fileName.textContent = fileData.name;

                const actions = document.createElement('div');
                actions.className = 'file-actions';

                const deleteBtn = document.createElement('button');
                deleteBtn.className = 'action-btn delete-btn';
                deleteBtn.innerHTML = '<i class="fas fa-trash"></i>';
                deleteBtn.onclick = (e) => {
                    e.stopPropagation();
                    this.deleteFile(fileIndex);
                };

                const selectBtn = document.createElement('button');
                selectBtn.className = 'action-btn select-btn';
                selectBtn.innerHTML = '<i class="fas fa-check"></i>';
                selectBtn.onclick = (e) => {
                    e.stopPropagation();
                    this.toggleFileSelection(fileIndex, fileDiv);
                };

                const startOverBtn = document.createElement('button');
                startOverBtn.className = 'action-btn start-over-btn';
                startOverBtn.innerHTML = '<i class="fas fa-undo"></i>';
                startOverBtn.onclick = (e) => {
                    e.stopPropagation();
                    this.reset();
                };

                actions.appendChild(deleteBtn);
                actions.appendChild(selectBtn);
                actions.appendChild(startOverBtn);

                info.appendChild(fileName);
                info.appendChild(actions);

                fileDiv.appendChild(preview);
                fileDiv.appendChild(info);

                // Add drag event listeners
                fileDiv.addEventListener('dragstart', (e) => this.handleFileDragStart(e, fileIndex));
                fileDiv.addEventListener('dragend', (e) => this.handleFileDragEnd(e));

                return fileDiv;
            }

            handleFileDragStart(event, fileIndex) {
                this.draggedElement = event.target;
                this.draggedIndex = fileIndex;
                event.target.classList.add('dragging');
                event.dataTransfer.effectAllowed = 'move';
                event.dataTransfer.setData('text/html', event.target.outerHTML);
            }

            handleFileDragEnd(event) {
                event.target.classList.remove('dragging');
                this.draggedElement = null;
                this.draggedIndex = null;
            }

            handleFileDrop(event) {
                if (this.draggedIndex === null) return;

                const dropTarget = event.target.closest('.file-card');
                if (!dropTarget) return;

                const dropIndex = parseInt(dropTarget.dataset.fileIndex);

                if (this.draggedIndex !== dropIndex) {
                    this.moveFile(this.draggedIndex, dropIndex);
                }
            }

            moveFile(fromIndex, toIndex) {
                const file = this.files.splice(fromIndex, 1)[0];
                this.files.splice(toIndex, 0, file);
                this.renderFiles();
            }

            toggleFileSelection(fileIndex, fileElement) {
                if (this.selectedFiles.has(fileIndex)) {
                    this.selectedFiles.delete(fileIndex);
                    fileElement.classList.remove('selected');
                    this.files[fileIndex].selected = false;
                } else {
                    this.selectedFiles.add(fileIndex);
                    fileElement.classList.add('selected');
                    this.files[fileIndex].selected = true;
                }
            }

            deleteFile(fileIndex) {
                this.showConfirmation('Are you sure you want to delete this file?', () => {
                    this.files.splice(fileIndex, 1);
                    this.selectedFiles.delete(fileIndex);

                    if (this.files.length === 0) {
                        this.reset();
                    } else {
                        this.renderFiles();
                    }
                });
            }

            sortFiles() {
                this.files.sort((a, b) => a.name.localeCompare(b.name));
                this.renderFiles();
                this.showAlert('Files sorted alphabetically.');
            }

            async mergeFiles() {
                if (this.files.length < 2) {
                    this.showAlert('Please upload at least 2 PDF files to merge.');
                    return;
                }

                try {
                    this.showLoading(true);

                    const mergedPdf = await PDFLib.PDFDocument.create();

                    for (const fileData of this.files) {
                        const pageCount = fileData.pdfDoc.getPageCount();
                        const pages = await mergedPdf.copyPages(fileData.pdfDoc, Array.from({length: pageCount}, (_, i) => i));
                        pages.forEach(page => mergedPdf.addPage(page));
                    }

                    this.processedPdf = mergedPdf;
                    this.showAlert('Files merged successfully! You can now download the merged PDF.');

                } catch (error) {
                    console.error('Error merging files:', error);
                    this.showAlert('Error merging files. Please try again.');
                } finally {
                    this.showLoading(false);
                }
            }

            async downloadPDF() {
                if (!this.processedPdf) {
                    await this.mergeFiles();
                }

                if (!this.processedPdf) {
                    this.showAlert('Please merge files first or upload PDF files.');
                    return;
                }

                try {
                    this.showLoading(true);

                    const pdfBytes = await this.processedPdf.save();
                    const blob = new Blob([pdfBytes], { type: 'application/pdf' });
                    const url = URL.createObjectURL(blob);

                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'organized-pdf.pdf';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);

                } catch (error) {
                    console.error('Error downloading PDF:', error);
                    this.showAlert('Error downloading PDF. Please try again.');
                } finally {
                    this.showLoading(false);
                }
            }

            reset() {
                this.showConfirmation('Are you sure you want to start over? All changes will be lost.', () => {
                    this.files = [];
                    this.selectedFiles.clear();
                    this.processedPdf = null;

                    document.getElementById('filesGrid').innerHTML = '';
                    document.getElementById('fileInput').value = '';
                    this.hideOrganizer();
                });
            }

            showOrganizer() {
                document.getElementById('homePage').style.display = 'none';
                document.getElementById('organizerPage').style.display = 'block';
            }

            hideOrganizer() {
                document.getElementById('homePage').style.display = 'flex';
                document.getElementById('organizerPage').style.display = 'none';
            }

            showLoading(show) {
                const modal = document.getElementById('loadingModal');
                modal.style.display = show ? 'flex' : 'none';
            }

            // Custom Alert/Confirmation Modals
            showAlert(message) {
                const alertModal = document.createElement('div');
                alertModal.className = 'modal';
                alertModal.style.display = 'flex';
                alertModal.innerHTML = `
                    <div class="modal-content">
                        <p>${message}</p>
                        <button class="footer-btn btn-primary" style="margin-top: 20px;" onclick="document.body.removeChild(this.parentNode.parentNode)">OK</button>
                    </div>
                `;
                document.body.appendChild(alertModal);
            }

            showConfirmation(message, onConfirm) {
                const confirmModal = document.createElement('div');
                confirmModal.className = 'modal';
                confirmModal.style.display = 'flex';
                confirmModal.innerHTML = `
                    <div class="modal-content">
                        <p>${message}</p>
                        <div style="margin-top: 20px; display: flex; justify-content: center; gap: 10px;">
                            <button class="footer-btn btn-primary" id="confirmYes">Yes</button>
                            <button class="footer-btn btn-secondary" id="confirmNo">No</button>
                        </div>
                    </div>
                `;
                document.body.appendChild(confirmModal);

                document.getElementById('confirmYes').onclick = () => {
                    onConfirm();
                    document.body.removeChild(confirmModal);
                };
                document.getElementById('confirmNo').onclick = () => {
                    document.body.removeChild(confirmModal);
                };
            }
        }

        // Initialize the PDF Organizer when the page loads
        document.addEventListener('DOMContentLoaded', () => {
            new PDFOrganizer();
        });
        // script.js content ends here